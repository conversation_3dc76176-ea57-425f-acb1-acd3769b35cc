import type { ThemeConfig } from "../types/theme";

/** Get system color scheme preference */
export function getSystemTheme(): "light" | "dark" {
  if (typeof window === "undefined") return "light";
  return window.matchMedia("(prefers-color-scheme: dark)").matches
    ? "dark"
    : "light";
}

/** Determine if the current config should use dark mode */
export function isDarkMode(config: ThemeConfig): boolean {
  return config.mode === "auto"
    ? getSystemTheme() === "dark"
    : config.mode === "dark";
}

/** Detect available theme variants by scanning CSS rules (no caching) */
export function getAvailableVariants(): string[] {
  if (typeof document === "undefined") return ["default"];

  const variants = new Set<string>(["default"]);

  for (const sheet of document.styleSheets) {
    let rules: CSSRuleList | undefined;

    try {
      rules = sheet.cssRules;
    } catch {
      continue; // Skip CORS-protected stylesheets
    }

    for (const rule of rules || []) {
      if (!(rule instanceof CSSStyleRule)) continue;

      const match = rule.selectorText.match(
        /\.theme-([a-zA-Z0-9_-]+)(?=\s|$|\.|\[|:)/
      );

      if (match) {
        const variant = match[1];
        if (variant !== "default") {
          variants.add(variant);
        }
      }
    }
  }

  return Array.from(variants).sort();
}

/** Clear all theme-related classes from the <html> element */
export function clearThemeClasses(classList: DOMTokenList): void {
  classList.remove("light", "dark");

  for (const cls of Array.from(classList)) {
    if (cls.startsWith("theme-")) {
      classList.remove(cls);
    }
  }
}

/** Apply theme mode and variant as classes on the <html> element */
export function applyTheme(config: ThemeConfig): void {
  if (typeof document === "undefined") return;

  const classList = document.documentElement.classList;
  clearThemeClasses(classList);

  const resolvedMode = config.mode === "auto" ? getSystemTheme() : config.mode;
  classList.add(resolvedMode);

  if (config.variant !== "default") {
    classList.add(`theme-${config.variant}`);
  }
}
