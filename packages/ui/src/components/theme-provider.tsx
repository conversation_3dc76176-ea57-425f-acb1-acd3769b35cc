import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  useMemo,
  useCallback,
} from "react";
import type {
  ThemeConfig,
  ThemeMode,
  ThemeVariant,
  UseThemeReturn,
} from "../types/theme";
import { THEME_MODES, DEFAULT_CONFIG } from "../lib/constants";
import { getStoredTheme, persistTheme } from "../utils/storage";
import { isDarkMode, applyTheme, getAvailableVariants } from "../utils/theme";

// Theme context
const ThemeContext = createContext<UseThemeReturn | undefined>(undefined);

// Props
export interface ThemeProviderProps {
  children: React.ReactNode;
  defaultTheme?: Partial<ThemeConfig>;
  storageKey?: string;
  disableStorage?: boolean;
}

export function ThemeProvider({
  children,
  defaultTheme,
  storageKey = "theme",
  disableStorage = false,
}: ThemeProviderProps) {
  // Initialize theme config
  const [config, setConfig] = useState<ThemeConfig>(() => {
    const stored = disableStorage ? null : getStoredTheme(storageKey);
    return {
      ...DEFAULT_CONFIG,
      ...stored,
      ...defaultTheme,
    };
  });

  const { mode, variant } = config;

  // Apply theme and persist on config change
  useEffect(() => {
    applyTheme(config);
    if (!disableStorage) persistTheme(config, storageKey);
  }, [config, disableStorage, storageKey]);

  // React to system theme if in auto mode
  useEffect(() => {
    if (mode !== "auto") return;

    const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
    const handleChange = () => applyTheme(config);

    mediaQuery.addEventListener("change", handleChange);
    return () => mediaQuery.removeEventListener("change", handleChange);
  }, [mode, config]);

  const availableVariants = useMemo(getAvailableVariants, []);
  const isDark = useMemo(() => isDarkMode(config), [config]);

  // Theme actions
  const setMode = useCallback((mode: ThemeMode) => {
    setConfig((prev) => ({ ...prev, mode }));
  }, []);

  const setVariant = useCallback((variant: ThemeVariant) => {
    setConfig((prev) => ({ ...prev, variant }));
  }, []);

  const toggleMode = useCallback(() => {
    setConfig((prev) => {
      const next =
        THEME_MODES[(THEME_MODES.indexOf(prev.mode) + 1) % THEME_MODES.length];
      return { ...prev, mode: next };
    });
  }, []);

  const cycleVariant = useCallback(() => {
    setConfig((prev) => {
      const index = availableVariants.indexOf(prev.variant);
      const next = availableVariants[(index + 1) % availableVariants.length];
      return { ...prev, variant: next };
    });
  }, [availableVariants]);

  // Context value
  const value = useMemo<UseThemeReturn>(
    () => ({
      mode,
      variant,
      isDark,
      availableVariants,
      setMode,
      setVariant,
      toggleMode,
      cycleVariant,
    }),
    [
      mode,
      variant,
      isDark,
      availableVariants,
      setMode,
      setVariant,
      toggleMode,
      cycleVariant,
    ]
  );

  return (
    <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>
  );
}

export function useTheme(): UseThemeReturn {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error("useTheme must be used within a ThemeProvider");
  }
  return context;
}
